"""
Test script to verify automatic stage progression fixes for stages 4→5, 5→6, and 6→7.

This test simulates the workflow conditions that should trigger automatic stage transitions
and verifies that the transitions occur correctly with st.rerun() calls.
"""

import pytest
import streamlit as st
from unittest.mock import Mock, patch, MagicMock
from state_manager import StateManager, StateStage


class TestAutomaticStageProgression:
    """Test class for automatic stage progression functionality."""

    def setup_method(self):
        """Set up test fixtures before each test method."""
        # Create a mock state manager
        self.state = Mock(spec=StateManager)
        self.state.current_stage = StateStage.STAGE4_DETECT
        self.state.advance_to = Mock(return_value=True)

        # Mock session state
        self.mock_session_state = {}

    def test_stage4_to_stage5_manual_element_selection_with_test_data(self):
        """Test Stage 4 → Stage 5 transition after manual element selection requiring test data."""
        # Setup: Manual element selection completed, test data required
        test_data_analysis = {
            "requires_test_data": True,
            "reason": "This step requires user input data"
        }

        # Mock the state conditions for Stage 4 completion
        self.state.current_stage = StateStage.STAGE4_DETECT

        # Simulate the conditions that should trigger Stage 4 → Stage 5 transition
        with patch('streamlit.session_state', self.mock_session_state):
            with patch('streamlit.rerun') as mock_rerun:
                with patch('streamlit.success') as mock_success:
                    with patch('streamlit.info') as mock_info:
                        # Simulate the manual element selection completion logic
                        if not test_data_analysis["requires_test_data"]:
                            # Should advance to Stage 6
                            expected_stage = StateStage.STAGE6_GENERATE
                            expected_message = "Element matching completed, no test data needed - advancing to Stage 6"
                        else:
                            # Should advance to Stage 5
                            expected_stage = StateStage.STAGE5_DATA
                            expected_message = "Element matching completed - advancing to Stage 5 for test data"

                        # Trigger the transition logic
                        if self.state.current_stage == StateStage.STAGE4_DETECT:
                            self.state.advance_to(expected_stage, expected_message)
                            self.mock_session_state['state'] = self.state
                            self.mock_session_state['stage_progression_message'] = "✅ Element matching completed. Proceeding to Stage 5 for test data configuration."
                            mock_rerun()

        # Verify the transition was called correctly
        self.state.advance_to.assert_called_once_with(expected_stage, expected_message)
        mock_rerun.assert_called_once()
        assert self.mock_session_state['stage_progression_message'] == "✅ Element matching completed. Proceeding to Stage 5 for test data configuration."

    def test_stage4_to_stage6_navigation_step_no_test_data(self):
        """Test Stage 4 → Stage 6 transition for navigation steps that don't require test data."""
        # Setup: Navigation step completed, no test data required
        test_data_analysis = {
            "requires_test_data": False,
            "reason": "Navigation step doesn't require test data"
        }

        # Mock the state conditions for Stage 4 completion
        self.state.current_stage = StateStage.STAGE4_DETECT

        # Simulate the conditions that should trigger Stage 4 → Stage 6 transition
        with patch('streamlit.session_state', self.mock_session_state):
            with patch('streamlit.rerun') as mock_rerun:
                # Simulate the navigation step completion logic
                if not test_data_analysis["requires_test_data"]:
                    expected_stage = StateStage.STAGE6_GENERATE
                    expected_message = "Navigation step completed, no test data needed - advancing to Stage 6"

                    # Trigger the transition logic
                    if self.state.current_stage == StateStage.STAGE4_DETECT:
                        self.state.advance_to(expected_stage, expected_message)
                        self.mock_session_state['state'] = self.state
                        self.mock_session_state['stage_progression_message'] = "✅ Navigation step completed. Proceeding to Stage 6 (no test data needed)."
                        mock_rerun()

        # Verify the transition was called correctly
        self.state.advance_to.assert_called_once_with(expected_stage, expected_message)
        mock_rerun.assert_called_once()
        assert self.mock_session_state['stage_progression_message'] == "✅ Navigation step completed. Proceeding to Stage 6 (no test data needed)."

    def test_stage5_to_stage6_test_data_skipped(self):
        """Test Stage 5 → Stage 6 transition when test data is skipped."""
        # Setup: Test data skipped
        self.state.current_stage = StateStage.STAGE5_DATA

        # Simulate the conditions that should trigger Stage 5 → Stage 6 transition
        with patch('streamlit.session_state', self.mock_session_state):
            with patch('streamlit.rerun') as mock_rerun:
                # Simulate the skip test data button logic
                expected_stage = StateStage.STAGE6_GENERATE
                expected_message = "Test data skipped - advancing to Stage 6"

                # Trigger the transition logic
                if self.state.current_stage == StateStage.STAGE5_DATA:
                    self.state.advance_to(expected_stage, expected_message)
                    self.mock_session_state['state'] = self.state
                    self.mock_session_state['stage_progression_message'] = "✅ Test data skipped. Proceeding to Stage 6."
                    mock_rerun()

        # Verify the transition was called correctly
        self.state.advance_to.assert_called_once_with(expected_stage, expected_message)
        mock_rerun.assert_called_once()
        assert self.mock_session_state['stage_progression_message'] == "✅ Test data skipped. Proceeding to Stage 6."

    def test_stage5_to_stage6_test_data_generated(self):
        """Test Stage 5 → Stage 6 transition when test data is auto-generated."""
        # Setup: Test data generated
        self.state.current_stage = StateStage.STAGE5_DATA

        # Simulate the conditions that should trigger Stage 5 → Stage 6 transition
        with patch('streamlit.session_state', self.mock_session_state):
            with patch('streamlit.rerun') as mock_rerun:
                # Simulate the auto-generate test data completion logic
                expected_stage = StateStage.STAGE6_GENERATE
                expected_message = "Test data generated - advancing to Stage 6"

                # Trigger the transition logic
                if self.state.current_stage == StateStage.STAGE5_DATA:
                    self.state.advance_to(expected_stage, expected_message)
                    self.mock_session_state['state'] = self.state
                    self.mock_session_state['stage_progression_message'] = "✅ Test data generated. Proceeding to Stage 6."
                    mock_rerun()

        # Verify the transition was called correctly
        self.state.advance_to.assert_called_once_with(expected_stage, expected_message)
        mock_rerun.assert_called_once()
        assert self.mock_session_state['stage_progression_message'] == "✅ Test data generated. Proceeding to Stage 6."

    def test_stage5_to_stage6_manual_test_data_saved(self):
        """Test Stage 5 → Stage 6 transition when manual test data is saved."""
        # Setup: Manual test data saved
        self.state.current_stage = StateStage.STAGE5_DATA

        # Simulate the conditions that should trigger Stage 5 → Stage 6 transition
        with patch('streamlit.session_state', self.mock_session_state):
            with patch('streamlit.rerun') as mock_rerun:
                # Simulate the manual test data save logic
                expected_stage = StateStage.STAGE6_GENERATE
                expected_message = "Manual test data saved - advancing to Stage 6"

                # Trigger the transition logic
                if self.state.current_stage == StateStage.STAGE5_DATA:
                    self.state.advance_to(expected_stage, expected_message)
                    self.mock_session_state['state'] = self.state
                    self.mock_session_state['stage_progression_message'] = "✅ Test data saved. Proceeding to Stage 6."
                    mock_rerun()

        # Verify the transition was called correctly
        self.state.advance_to.assert_called_once_with(expected_stage, expected_message)
        mock_rerun.assert_called_once()
        assert self.mock_session_state['stage_progression_message'] == "✅ Test data saved. Proceeding to Stage 6."

    def test_stage6_to_stage7_script_validated(self):
        """Test Stage 6 → Stage 7 transition when script is validated."""
        # Setup: Script validated
        self.state.current_stage = StateStage.STAGE6_GENERATE

        # Simulate the conditions that should trigger Stage 6 → Stage 7 transition
        with patch('streamlit.session_state', self.mock_session_state):
            with patch('streamlit.rerun') as mock_rerun:
                # Simulate the script validation completion logic
                expected_stage = StateStage.STAGE7_EXECUTE
                expected_message = "Script validated - advancing to Stage 7"

                # Trigger the transition logic
                if self.state.current_stage == StateStage.STAGE6_GENERATE:
                    self.state.advance_to(expected_stage, expected_message)
                    self.mock_session_state['state'] = self.state
                    self.mock_session_state['stage_progression_message'] = "✅ Script validated. Proceeding to Stage 7."
                    mock_rerun()

        # Verify the transition was called correctly
        self.state.advance_to.assert_called_once_with(expected_stage, expected_message)
        mock_rerun.assert_called_once()
        assert self.mock_session_state['stage_progression_message'] == "✅ Script validated. Proceeding to Stage 7."

    def test_stage6_to_stage7_script_generated(self):
        """Test Stage 6 → Stage 7 transition when script is generated without validation."""
        # Setup: Script generated
        self.state.current_stage = StateStage.STAGE6_GENERATE

        # Simulate the conditions that should trigger Stage 6 → Stage 7 transition
        with patch('streamlit.session_state', self.mock_session_state):
            with patch('streamlit.rerun') as mock_rerun:
                # Simulate the script generation completion logic
                expected_stage = StateStage.STAGE7_EXECUTE
                expected_message = "Script generated - advancing to Stage 7"

                # Trigger the transition logic
                if self.state.current_stage == StateStage.STAGE6_GENERATE:
                    self.state.advance_to(expected_stage, expected_message)
                    self.mock_session_state['state'] = self.state
                    self.mock_session_state['stage_progression_message'] = "✅ Script generated successfully. Proceeding to Stage 7."
                    mock_rerun()

        # Verify the transition was called correctly
        self.state.advance_to.assert_called_once_with(expected_stage, expected_message)
        mock_rerun.assert_called_once()
        assert self.mock_session_state['stage_progression_message'] == "✅ Script generated successfully. Proceeding to Stage 7."

    def test_stage7_to_stage8_all_steps_completed_success(self):
        """Test Stage 7 → Stage 8 transition when all test steps are completed successfully."""
        # Setup: All test steps completed successfully
        self.state.current_stage = StateStage.STAGE7_EXECUTE
        self.state.all_steps_done = True

        # Simulate the conditions that should trigger Stage 7 → Stage 8 transition
        with patch('streamlit.session_state', self.mock_session_state):
            with patch('streamlit.rerun') as mock_rerun:
                # Simulate the all steps completion logic
                expected_stage = StateStage.STAGE8_OPTIMIZE
                expected_message = "All test steps completed - automatically advancing to Stage 8"

                # Trigger the transition logic
                if self.state.current_stage == StateStage.STAGE7_EXECUTE:
                    self.state.advance_to(expected_stage, expected_message)
                    self.mock_session_state['state'] = self.state
                    self.mock_session_state['stage_progression_message'] = "✅ All test steps completed. Proceeding to Script Optimization (Stage 8)."
                    mock_rerun()

        # Verify the transition was called correctly
        self.state.advance_to.assert_called_once_with(expected_stage, expected_message)
        mock_rerun.assert_called_once()
        assert self.mock_session_state['stage_progression_message'] == "✅ All test steps completed. Proceeding to Script Optimization (Stage 8)."

    def test_stage7_to_stage8_error_acknowledged_all_steps_done(self):
        """Test Stage 7 → Stage 8 transition when error is acknowledged and all steps are completed."""
        # Setup: Error acknowledged, all steps completed
        self.state.current_stage = StateStage.STAGE7_EXECUTE
        self.state.all_steps_done = True
        step_no = "3"

        # Simulate the conditions that should trigger Stage 7 → Stage 8 transition
        with patch('streamlit.session_state', self.mock_session_state):
            with patch('streamlit.rerun') as mock_rerun:
                # Simulate the error acknowledgment with all steps completed logic
                expected_stage = StateStage.STAGE8_OPTIMIZE
                expected_message = f"Error acknowledged for step {step_no}, all steps completed - advancing to Stage 8"

                # Trigger the transition logic
                if self.state.current_stage == StateStage.STAGE7_EXECUTE:
                    self.state.advance_to(expected_stage, expected_message)
                    self.mock_session_state['state'] = self.state
                    self.mock_session_state['stage_progression_message'] = f"⚠️ Step {step_no} error acknowledged. All steps completed. Proceeding to Script Optimization (Stage 8)."
                    mock_rerun()

        # Verify the transition was called correctly
        self.state.advance_to.assert_called_once_with(expected_stage, expected_message)
        mock_rerun.assert_called_once()
        assert self.mock_session_state['stage_progression_message'] == f"⚠️ Step {step_no} error acknowledged. All steps completed. Proceeding to Script Optimization (Stage 8)."

    def test_stage7_to_stage4_error_acknowledged_more_steps_remaining(self):
        """Test Stage 7 → Stage 4 transition when error is acknowledged but more steps remain."""
        # Setup: Error acknowledged, more steps remaining
        self.state.current_stage = StateStage.STAGE7_EXECUTE
        self.state.all_steps_done = False
        step_no = "2"

        # Simulate the conditions that should trigger Stage 7 → Stage 4 transition
        with patch('streamlit.session_state', self.mock_session_state):
            with patch('streamlit.rerun') as mock_rerun:
                # Simulate the error acknowledgment with more steps remaining logic
                # This should NOT advance to Stage 8, but should return to Stage 4

                # Since all_steps_done is False, should not advance to Stage 8
                # Instead should set session state for Stage 4 transition
                self.mock_session_state['state'] = self.state
                self.mock_session_state['stage_progression_message'] = f"⚠️ Step {step_no} execution failed. Error acknowledged."
                mock_rerun()

        # Verify no advance_to was called (since we're going back to Stage 4, not forward to Stage 8)
        self.state.advance_to.assert_not_called()
        mock_rerun.assert_called_once()
        assert self.mock_session_state['stage_progression_message'] == f"⚠️ Step {step_no} execution failed. Error acknowledged."


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
